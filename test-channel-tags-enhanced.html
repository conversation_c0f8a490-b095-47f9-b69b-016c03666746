<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>频道标签模块精细调整测试</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            border-bottom: 2px solid #FF6B35;
            padding-bottom: 8px;
        }
        
        .enhancement-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .enhancement-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            border-left: 4px solid #FF6B35;
        }
        
        .enhancement-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .enhancement-content {
            font-size: 13px;
            color: #666;
            line-height: 1.5;
        }
        
        .wordcloud-highlight {
            background: #e3f2fd;
            border-left: 4px solid #1976d2;
            padding: 10px 15px;
            margin: 10px 0;
            border-radius: 0 4px 4px 0;
        }
        
        .percentage-update {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 10px 15px;
            margin: 10px 0;
            border-radius: 0 4px 4px 0;
        }
        
        .test-link {
            display: inline-block;
            background: #FF6B35;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            margin: 10px 10px 10px 0;
            transition: background-color 0.2s;
        }
        
        .test-link:hover {
            background: #e55a2b;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 10px;
        }
        
        .status-enhanced {
            background: #e8f5e8;
            color: #4caf50;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 13px;
        }
        
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #e0e0e0;
            padding: 8px 12px;
            text-align: left;
        }
        
        .comparison-table th {
            background-color: #f5f5f5;
            font-weight: 600;
            color: #333;
        }
        
        .comparison-table .before {
            color: #f44336;
        }
        
        .comparison-table .after {
            color: #4caf50;
            font-weight: 500;
        }
        
        .visual-demo {
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            background: white;
        }
        
        .demo-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .size-demo {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: baseline;
            justify-content: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            margin: 10px 0;
        }
        
        .size-demo .tag-item {
            margin: 5px;
        }
        
        @media (max-width: 768px) {
            .enhancement-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>频道标签模块精细调整测试</h1>
        <p style="color: #666; margin-bottom: 20px;">验证网红类别百分比调整和标签云词云化优化效果</p>
        
        <!-- 调整概述 -->
        <div class="test-section">
            <div class="test-title">精细调整概述 <span class="status-badge status-enhanced">✓ 已优化增强</span></div>
            <p style="color: #666; line-height: 1.6;">
                对频道标签模块进行了两项重要的精细调整：网红类别百分比从均匀分布改为真实的不均匀分布，标签云从4级扩展为6级词云效果，字体范围从11px-18px扩大到9px-24px，创造更丰富的视觉层次和更真实的数据展示。
            </p>
        </div>

        <!-- 调整详情 -->
        <div class="test-section">
            <div class="test-title">调整详情</div>
            
            <div class="enhancement-grid">
                <div class="enhancement-item">
                    <div class="enhancement-title">网红类别百分比调整</div>
                    <div class="enhancement-content">
                        <strong>调整前：</strong>均匀分布<br>
                        • 娱乐：25%<br>
                        • 电影：25%<br>
                        • 孩子：25%<br>
                        • 教育：25%<br><br>
                        
                        <strong>调整后：</strong>真实分布<br>
                        • 娱乐：45%（最大）<br>
                        • 孩子：25%<br>
                        • 教育：20%<br>
                        • 电影：10%（最小）
                    </div>
                </div>
                
                <div class="enhancement-item">
                    <div class="enhancement-title">标签云词云化优化</div>
                    <div class="enhancement-content">
                        <strong>层级扩展：</strong><br>
                        • 从4级扩展到6级<br>
                        • 字体范围：9px - 24px<br>
                        • 更丰富的视觉层次<br><br>
                        
                        <strong>重要标签突出：</strong><br>
                        • cocomelon（24px，最大）<br>
                        • nurseryrhymes（21px）<br>
                        • kids education nursery rhymes（21px）<br>
                        • 核心标签优先排列
                    </div>
                </div>
            </div>
        </div>

        <!-- 字体大小层级演示 -->
        <div class="test-section">
            <div class="test-title">6级字体大小层级演示</div>
            
            <div class="visual-demo">
                <div class="demo-title">桌面端字体大小（9px - 24px）</div>
                <div class="size-demo">
                    <span class="tag-item size-1">size-1 (9px)</span>
                    <span class="tag-item size-2">size-2 (12px)</span>
                    <span class="tag-item size-3">size-3 (15px)</span>
                    <span class="tag-item size-4">size-4 (18px)</span>
                    <span class="tag-item size-5">size-5 (21px)</span>
                    <span class="tag-item size-6">size-6 (24px)</span>
                </div>
            </div>
            
            <div class="wordcloud-highlight">
                <strong>词云效果增强：</strong><br>
                • 字体大小差异更加明显（15px范围 vs 原7px范围）<br>
                • 颜色深浅对比度增强（#999 → #1d4ed8）<br>
                • 字重层次丰富（400 → 800）<br>
                • 重要标签视觉突出度显著提升
            </div>
        </div>

        <!-- 进度条宽度对比 -->
        <div class="test-section">
            <div class="test-title">网红类别进度条宽度调整</div>
            
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>类别</th>
                        <th>调整前</th>
                        <th>调整后</th>
                        <th>进度条宽度</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>娱乐</td>
                        <td class="before">25%</td>
                        <td class="after">45%</td>
                        <td>100%（基准）</td>
                    </tr>
                    <tr>
                        <td>孩子</td>
                        <td class="before">25%</td>
                        <td class="after">25%</td>
                        <td>56%（相对娱乐）</td>
                    </tr>
                    <tr>
                        <td>教育</td>
                        <td class="before">25%</td>
                        <td class="after">20%</td>
                        <td>44%（相对娱乐）</td>
                    </tr>
                    <tr>
                        <td>电影</td>
                        <td class="before">25%</td>
                        <td class="after">10%</td>
                        <td>22%（相对娱乐）</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="percentage-update">
                <strong>视觉效果改进：</strong><br>
                • 进度条长度真实反映数据差异<br>
                • 娱乐类别占主导地位，视觉上更加突出<br>
                • 电影类别最小，符合实际数据分布<br>
                • 保持原有的4色配色方案
            </div>
        </div>

        <!-- 响应式表现 -->
        <div class="test-section">
            <div class="test-title">响应式表现优化</div>
            <div style="color: #666; line-height: 1.6;">
                <p><strong>桌面端（>1200px）：</strong></p>
                <ul>
                    <li>6级字体大小：9px、12px、15px、18px、21px、24px</li>
                    <li>最佳词云视觉效果，重要标签突出</li>
                    <li>进度条宽度准确反映百分比差异</li>
                </ul>
                
                <p><strong>平板端（768px-1200px）：</strong></p>
                <ul>
                    <li>6级字体大小：8px、10px、13px、15px、18px、20px</li>
                    <li>保持词云层次感，适度压缩尺寸</li>
                    <li>进度条比例关系保持不变</li>
                </ul>
                
                <p><strong>移动端（<768px）：</strong></p>
                <ul>
                    <li>6级字体大小：7px、9px、11px、13px、15px、17px</li>
                    <li>最小化尺寸但保持可读性</li>
                    <li>词云效果在小屏幕上仍然清晰</li>
                </ul>
            </div>
        </div>

        <!-- 测试要点 -->
        <div class="test-section">
            <div class="test-title">测试要点</div>
            <div style="color: #666; line-height: 1.6;">
                <p><strong>重点验证项目：</strong></p>
                <ol>
                    <li><strong>网红类别百分比：</strong>确认显示45%、25%、20%、10%的不均匀分布</li>
                    <li><strong>进度条宽度：</strong>验证进度条长度准确反映百分比差异</li>
                    <li><strong>标签云层次：</strong>检查6级字体大小的视觉层次效果</li>
                    <li><strong>重要标签突出：</strong>确认cocomelon、nurseryrhymes等核心标签最大</li>
                    <li><strong>颜色对比度：</strong>验证蓝色系渐变的深浅对比</li>
                    <li><strong>响应式适配：</strong>在不同屏幕尺寸下测试效果</li>
                </ol>
                
                <p style="margin-top: 15px;"><strong>期望结果：</strong></p>
                <p>频道标签模块应该展现出更真实的数据分布和更丰富的词云视觉效果，提供更好的信息层次感和用户体验。</p>
            </div>
        </div>

        <!-- 测试链接 -->
        <div class="test-section">
            <div class="test-title">开始测试</div>
            <div style="text-align: center; padding: 20px;">
                <a href="index.html" class="test-link">
                    <i class="fas fa-external-link-alt"></i>
                    打开主页面测试精细调整效果
                </a>
            </div>
            <p style="text-align: center; color: #666; font-size: 14px; margin-top: 10px;">
                点击上方链接打开主页面，切换到"内容数据"页签，查看频道标签模块的精细调整效果
            </p>
        </div>
    </div>
</body>
</html>
